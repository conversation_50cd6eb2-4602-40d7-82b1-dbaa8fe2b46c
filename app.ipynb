{"cells": [{"cell_type": "code", "execution_count": null, "id": "9b8c9115", "metadata": {}, "outputs": [], "source": ["import cv2\n", "cap = cv2.VideoCapture(\"\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 5}