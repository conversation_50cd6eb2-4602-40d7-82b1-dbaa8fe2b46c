{"cells": [{"cell_type": "code", "execution_count": 2, "id": "baa0c0dd", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pymongo"]}, {"cell_type": "code", "execution_count": 3, "id": "d1345283", "metadata": {}, "outputs": [], "source": ["client = pymongo.MongoClient(\"mongodb://serveradmin:<EMAIL>:27017/\")\n", "db = client[\"Consolidated_AI_Models_API_DB\"]\n"]}, {"cell_type": "code", "execution_count": null, "id": "be77c6f5", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "module 'pymongo' has no attribute 'Regex'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m col \u001b[38;5;241m=\u001b[39m db[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtaskschemas\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m----> 3\u001b[0m results \u001b[38;5;241m=\u001b[39m col\u001b[38;5;241m.\u001b[39mfind({\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mapiBody\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[43mpymongo\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mRegex\u001b[49m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mXK8JzZYBrWwlXDp-ArOv\u001b[39m\u001b[38;5;124m\"\u001b[39m)})\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;28mlen\u001b[39m(results))\n", "\u001b[0;31mAttributeError\u001b[0m: module 'pymongo' has no attribute 'Regex'"]}], "source": ["col = db[\"taskschemas\"]\n", "\n", "results = col.find({\"apiBody\": { '$regex': \"XK8JzZYBrWwlXDp-ArOv\"}})\n", "print(len(results))"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 5}