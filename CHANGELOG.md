## [1.5.4](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.5.3...1.5.4) (2025-05-07)


### Bug Fixes

* add timeout to callback calls ([3c68e85](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/3c68e85fe031b5b010237743210f95c993e65ec9))

## [1.5.3](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.5.2...1.5.3) (2025-04-28)


### Bug Fixes

* Error occurred while processing image local variable 'faces_with_small_model' referenced before assignment. Do full face extraction if small  model fails. ([d0f8b09](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/d0f8b097ef55747fa7d1e8d15ae9687c0e43eadd))
* remove hardcoded GITLAB_PIP_TOKEN ([a877687](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/a8776876c0440a077fde0ba9d5cd99bf6fad6b7c))
* upload using docserver v2 if docserver v1 fails ([22dadc2](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/22dadc235c607f19ca8b00e87ade4bc69a416ac3))

## [1.5.2](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.5.1...1.5.2) (2025-03-19)


### Bug Fixes

* two similar faces in the same seconds , both bounding boxes needs to be added . should not be discarded. ([0627c4f](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/0627c4ffdcd42cccf2a7c0a11c3878dcfea3dfd5))

## [1.5.1](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.5.0...1.5.1) (2025-02-27)


### Bug Fixes

* update log level for image processing errors ([bb27d51](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/bb27d51b78494f313516a739db4d1d6f7ad95306))

# [1.5.0](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.4.2...1.5.0) (2025-02-26)


### Bug Fixes

* increase timeout connect and read. ([68fe0f5](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/68fe0f5ce5da4ef52a599c342c72c0e38e4f00ad))


### Features

* increased timeouts, added try catch for each face crop processing and uploading ([2ea3b0f](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/2ea3b0f7e25b6badae1ac56db170160be04d19f2))

## [1.4.2](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.4.1...1.4.2) (2025-02-20)


### Bug Fixes

* increased the timeout to 60 sec when uploading facecrops to docserver ([def9523](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/def9523c2f74e740257c63e0d8f21648bae3109c))

## [1.4.1](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.4.0...1.4.1) (2025-02-19)


### Bug Fixes

* added error clalback in case of error for videos ([c700f03](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/c700f032630aa8d3ae29104cb90479ad17ba3a7f))

# [1.4.0](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.17...1.4.0) (2025-02-06)


### Bug Fixes

* added logs ([6b7bcaa](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/6b7bcaa3585edfa3e303666c3832056724f8a10c))
* added retry for upload crop to docserver ([aac4ba6](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/aac4ba60ca48d876327c88a68ebeb1031f9c586c))
* changed the small face app to large model. ([0f0a0c6](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/0f0a0c6f2fb885621f34adfd4ff8d405b1697acf))
* fixed the bbox second precision issue ([b718d83](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/b718d83e100cf2d78382574c734b5ceb192d8ea0))
* fixing in the issue of uploading face crops to elastic ([0b17d90](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/0b17d901efc8ef25ee964743d216c5f15c941c2c))
* increased precision of bbox second to 5 decimal places ([37fe204](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/37fe20406a07129edc02c9a6f0ee02c2d6a83359))
* removed all the filtering for redaction ([6c33ef8](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/6c33ef8b8c020b50acb7042e028ba251f49e59ad))
* removed unnecessary logs ([00984ad](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/00984ad1ffde00c4c4c97b568532d9f167fe64d0))


### Features

* added selected frame second ([a39cf6f](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/a39cf6febebe6719136a2c4b2ab7ea6bfed87a0e))

## [1.3.17](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.16...1.3.17) (2025-01-17)


### Bug Fixes

* bug fix in the face cropping ([4cfe81a](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/4cfe81a8c4c7b3cc8556f60d6ae6e98db6f765e3))

## [1.3.16](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.15...1.3.16) (2025-01-10)


### Bug Fixes

* add tenacity to requirements for retry functionality ([bc03fd4](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/bc03fd4f8a0b651a3dbe858104da36b2ced63c2b))
* implement retry mechanism for callback requests ([8e71082](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/8e710827e8329e04b05df2061d6001daf477bf63))

## [1.3.15](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.14...1.3.15) (2024-12-12)


### Bug Fixes

* dummy commit ([3f45cfa](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/3f45cfae0a286c3a76d580cfc0e04be3c1d09016))

## [1.3.14](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.13...1.3.14) (2024-12-09)


### Bug Fixes

* added retry mechanism ([8d47367](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/8d47367c3fd4a8dd645d082ec77dbd94d637f292))

## [1.3.13](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.12...1.3.13) (2024-11-18)


### Bug Fixes

* minor bug in the blur score filtering ([1fc8322](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/1fc8322a9fb7280af16be771d31932446a2033e6))

## [1.3.12](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.11...1.3.12) (2024-11-18)


### Bug Fixes

* fixing the parameters to improve video fr accuracy ([e707b5d](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/e707b5df7a6a58eeda4b101d1bd0b7c7af848799))

## [1.3.11](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.10...1.3.11) (2024-11-18)


### Bug Fixes

* bug fix in the filtering of faces in the video processing due to blur score, added code for GUI debugging ([667578d](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/667578d4a96a8103d0fb861f604d1791da26b966))

## [1.3.10](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.9...1.3.10) (2024-11-14)


### Bug Fixes

* decreased the threshold values because it is filtering many faces ([5e00d3d](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/5e00d3d198d499b5ea0965e235e2b59195e609f3))

## [1.3.9](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.8...1.3.9) (2024-11-14)


### Bug Fixes

* increased onnx version ([8af93ef](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/8af93efa361e83c3f5950ed68131f00f115aa65d))
* increased the BLUR_CONFIDENCE_THRESHOLD, because the threshold is too low and lot of faces getting filtered ([8e99895](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/8e998956533a7d305105d2a26eca21399809a2a3))

## [1.3.8](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.7...1.3.8) (2024-11-11)


### Bug Fixes

* not filtering any faces if redaction is true ([be12978](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/be12978ccff497bb758325dcbda5e4da5c72f573))

## [1.3.7](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.6...1.3.7) (2024-11-11)


### Bug Fixes

* add versioning to solve incompatiblity issues ([57ba6e6](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/57ba6e6e7abacd3be0814e0e52c96f31d405ddce))
* downgraded onnxruntime-gpu to detect the cudnn 8 ([d6f7122](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/d6f71224a81971c4e6b1577abdbe5f9897be1b85))
* fix versioning issues ([bd9b05b](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/bd9b05b566d571f40410a13dfb6c8fd2ac0cb48d))
* python version conflict ([49eb65e](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/49eb65e0cec0a40f4177cdd3e8d3dc767ae320f4))
* removed versioning in python ([6f1d76a](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/6f1d76a6fac664963e2be12a1ccd7bff9e1de849))
* try downgrading the base image and onnx version ([aff0c12](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/aff0c12259686da12061eb8b94ed93fa1ca3a357))
* trying to fix the cuda issues ([564dcfe](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/564dcfe05807ef4d0d8140a55152304003358e8c))
* trying to fix the cuda issues ([57ce29a](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/57ce29ab1c7fe384090a2a8a0460af5d019ecec1))
* trying with different onnx version ([68cf6d2](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/68cf6d2d27cc23ef9827ca8a101d7a6bcc1ca75d))
* upgraded the nvidia cuda base image ([1174bcb](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/1174bcbb7913ae4db44c462afb90f460fb6ec0fd))

## [1.3.6](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.5...1.3.6) (2024-10-28)


### Bug Fixes

* removed the face_landmarks from returning as it is causing some issues in saving to elastic ([d3f0f51](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/d3f0f511bcc710d934f567879aaa795e937cf901))

## [1.3.5](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.4...1.3.5) (2024-09-18)


### Bug Fixes

* bbox_second is converted from float to int due to mapping issue in stg ([610a8b4](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/610a8b4df5fe3eb6621691f4c1af2a36978ac246))
* decreased the detection threshold and blur score threshold ([3466028](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/34660281455167f0e1389cb2073627c460da45f4))
* removed redundant logs ([15ec8a9](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/15ec8a9dc411fa68b0245d7951f73c44ab64f9fc))

## [1.3.4](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.3...1.3.4) (2024-09-17)


### Bug Fixes

* major bug, fixed the issue of not adding face info after the best aligned face is detected. ([3427947](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/3427947db96520d16001dc757e48a4168d4a2f12))

## [1.3.3](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.2...1.3.3) (2024-09-16)


### Bug Fixes

* bug fix in the docker file ([24a8d88](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/24a8d88a007960cb1d25fa63be7090b77a6566c4))
* dummy commit ([4e8eac3](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/4e8eac3d4ed7502e8b0a6a175c5ae526ffa34abe))
* major update 2, we have changed the structure of the persons payload for videos as prev format is causing issues to elastic by increasing the num of field mapper_parser_exception ([8662978](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/86629784e2ea6c56c0951be5e6b442b0651548ec))
* major update: improved the video proess and major increase in the accuracy by removing blurred, non-aligned faces, found a bug in face matching logic ([d637aad](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/d637aad009abbc344eaea47ca3d022dedf85ec79))
* minor bug fix ([629f8f0](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/629f8f064e918a016cbce6cfd2fb7bb5afb37cdf))
* minor conversion issues of the field values ([df8bb4b](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/df8bb4bd50e6736fca64ece0b11b03bfd9acd29e))
* update field "embedding" to "face_embedding" for videos ([0c7b98e](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/0c7b98e6469d8ea6dfc7737cce89b06d212ffdb4))

## [1.3.2](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.1...1.3.2) (2024-09-11)


### Bug Fixes

* dummy commit ([8df1be6](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/8df1be6cc0306af37b5976a7194a27bb7fcbb71c))

## [1.3.1](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.3.0...1.3.1) (2024-08-21)


### Bug Fixes

* reduced the bbox threshold ([11412a9](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/11412a955a91dc445db91fd98ba44617fe67bc02))

# [1.3.0](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.18...1.3.0) (2024-06-20)


### Bug Fixes

* **9278:** removing trailing slash ([f547193](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/f5471932f982831390d236541c19eb52633d14ad))


### Features

* added some ignore ([ba78819](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/ba78819db6ee1c1c25c1b856906137ee2ea58a93))

## [1.2.18](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.17...1.2.18) (2024-4-17)


### Bug Fixes

* reduced the threshold for detection, alignment, blurriness ([15a8ccc](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/15a8ccce942ad1bc5cdd41d020af691341baf779))
* small syntax error ([70d758b](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/70d758bfe963e6f86aaf1a7eaef91a10c242ded3))
* Update Dockerfile and init.sh scripts, migrated all the pip installations to the dockerfile ([be77fb8](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/be77fb8b7edb8173dd0da82086ec7b7f5784ed6f))

## [1.2.17](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.16...1.2.17) (2024-4-11)


### Bug Fixes

* Add logging statements for docserver uploads ([a35997c](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/a35997cb65b419f77dea71d722b30b14a213110b))
* Update DOC_SERVER_V1_URL in config.py and fix error handling in main.py ([0c0debe](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/0c0debe9480359c873f992cd8651cbc3a0f6df52))
* Update DOCSERVER_SWITCH value and port number in main.py ([a89fc98](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/a89fc985fc94ff0b4f817fe5153eaa4576fe1359))

## [1.2.16](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.15...1.2.16) (2024-3-21)


### Bug Fixes

* bug fix ([591f2a1](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/591f2a1d464a11e6fb420b1586bde9523d54142d))
* Commented out code for skipping file processing if already processed ([8306b72](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/8306b72e74a7e121eaa899225d17b41253d4e06b))
* Updated main.py skipping processing if it is already processed ([293cab0](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/293cab04cb9f1bb24161171df075828596726287))

## [1.2.15](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.14...1.2.15) (2024-2-29)


### Bug Fixes

* Add normalized face embedding to unique_faces dictionary ([1baf1e2](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/1baf1e28ea3523d215590e9d5837c891a33cb67e))

## [1.2.14](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.13...1.2.14) (2024-02-22)


### Bug Fixes

* Convert numpy types for faces_low_confidence ([8be86fd](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/8be86fd6433508157d1f416ad6683fd5dd85fdcf))
* Update extend_bbox to use 30% percent for face extension ([05a79a0](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/05a79a063a60393b48603788a639f0235eb90e5c))

## [1.2.13](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.12...1.2.13) (2024-02-20)


### Bug Fixes

* Add DEBIAN_FRONTEND environment variable to init.sh script ([34cda42](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/34cda42d71b974c63c93cb1c0ee2577584b81b48))
* Add libglib2.0-0 installation to init.sh ([b5c185a](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/b5c185ae361e75001b37e1e60cb795ee3b7ae011))
* Commented out code for updating docserver ([c0e3860](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/c0e38601a7f9dfaf4ff31bfd4c2a4716cf68b606))
* removed relationship creation, added libgl package in docker ([b3fb309](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/b3fb309f29237950cb292fbe160248f431040038))
* sending vdb payload in the callback payload ([37930ab](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/37930ab5cfccb8860052f924ad66da3c7e0fdde1))

## [1.2.12](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.11...1.2.12) (2024-02-06)


### Bug Fixes

* Refactor entity insertion into vector db ([22a9027](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/22a9027c20cf0c6845457bd341e76e4e58fa32d3))
* sending source_type in the similar response ([61735b3](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/61735b310cceba1bbbd58a299df215d45da9225b))

## [1.2.11](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.10...1.2.11) (2024-02-05)


### Bug Fixes

* added code for video processing, saving to vdb, relationships ([f07f104](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/f07f104a26bb6c79e483c49d22a9ac8652fc48ce))
* added logs ([e2a9d0a](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/e2a9d0a5f7539cfb465a303512981b6d7468378f))
* bug fix in docserverlib installation ([33e1043](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/33e1043e55f85e94830303bc82d59d30b3d8a1e7))
* cahnged the project structure, added dependencies in the init.sh ([f39d238](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/f39d23845200a2d60a71c9514b77a117d95c9899))
* just added logs ([27fd8a3](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/27fd8a3ab28f6bb5aa7e6ed1fb426c140e81e929))

## [1.2.10](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.9...1.2.10) (2024-02-05)


### Bug Fixes

* added endpoint to get the similar images in relationship format ([05d4ca1](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/05d4ca19f40b651df1801f0b165014f86cf80930))
* added logic to skip the file if the file_id has already been processed ([5dc5ef8](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/5dc5ef870931946135e928735cbd5fdc37358362))
* bug fix in the relationship endpoint ([a20b903](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/a20b9038d979c5766e8553e3cecffc89ecddb2d4))
* Fix return statement in get_similar_images_by_face_relationship_format ([bae2dcf](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/bae2dcf66cbb534c234806effd21f0ca683b9003))
* Refactor return statement in get_similar_images_by_face_relationship_format function ([d21f907](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/d21f907f6eb9d871a3273821c80706fb81ff5ba1))
* small bug fix ([c5442c7](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/c5442c737575c63e331e1ae6c8bddb47cc3968a3))

## [1.2.9](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.8...1.2.9) (2024-02-01)


### Bug Fixes

* added "durationInSeconds" for video processing ([d2ad227](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/d2ad2278b08025bf15c938fd849f563ff17df573))

## [1.2.8](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.7...1.2.8) (2024-01-30)


### Bug Fixes

* added is_profile_image to the face_collection ([cd5726a](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/cd5726ab7f682d1f4d5007dea237c69555d29b52))
* added keypoints to the file collection ([39fc6f7](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/39fc6f7fd71d040a1ff145440638276a30f434f0))
* added re-inserting vectordb code ([af1ff7c](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/af1ff7ced00a94b723f1b0d21b71f048fb101bc2))
* added source_type to the vector db insertion ([f16b1d9](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/f16b1d96233107d9e94a7baedac86dc7cb414f24))
* added startup to create face collection, index if doesn't exist, added event_id to vdb entry ([136bd12](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/136bd12a0ddfcc5a864877ba3a559391d38aa406))
* fixed small issue in dockerfile ([3b80f7b](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/3b80f7b121a8fc3fa7398fbe3dfeb191b1cf7f8a))
* moved config.py to correct location ([ed8e577](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/ed8e5773e1f7cae2b51468b98e7da56478f1481a))
* returning faces with low confidence also, for redaction ([58dd546](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/58dd546fec7233ef9db454da8b50ce8d4e13fefe))
* small bug fix ([326f6f4](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/326f6f45824929e6459f9b26ccac90cc18b246f9))

## [1.2.7](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.6...1.2.7) (2024-01-22)


### Bug Fixes

* add most_common_entity in return statement ([feb83fb](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/feb83fb6543e527b9a6456885d45e4e65341f290))

## [1.2.6](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.5...1.2.6) (2024-01-22)


### Bug Fixes

* added code to extract entity_name for most common entity_id ([e3ee60f](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/e3ee60fc261cb7aad9bd8835260c1da2b5bca66f))

## [1.2.5](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.4...1.2.5) (2024-01-21)


### Bug Fixes

* changed the face_collection to face_collection_2 in main ([786a1b1](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/786a1b19560907d7f77ca7ca16de267b2f0d31cf))

## [1.2.4](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.3...1.2.4) (2024-01-21)


### Bug Fixes

* added logic to get the entity id from docserver, if the entity_id is not there when inserting into vector db ([8f7f9b7](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/8f7f9b79edab5922d8c91ded4fad5078f273d259))
* removed blurness filter while inserting into vdb, added most_common_entity_id ([1c300bd](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/1c300bdf6031a7199b9ab7ec2253f3668546d330))

## [1.2.3](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.2...1.2.3) (2024-01-19)


### Bug Fixes

* added ARGS in dockerfile ([46e9639](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/46e963973be7b718eeab18e9259b9a55596b39cc))
* added ds_client to update the file ([901cf9c](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/901cf9c96fb4805d3357add36be8af68ab64202f))
* removed ds_client again because api calling issue is fixed by Ansuman ([2ee2254](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/2ee22545dabde43b40d1aebce2c7137eb7d7ffae))
* removed few services from ds_client ([fd54123](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/fd5412314560ec36a666739a81ac3e775b6e5f03))

## [1.2.2](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.1...1.2.2) (2024-01-19)


### Bug Fixes

* changed the code to update docserver from ingester to old docserver ([04ed396](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/04ed396292a713d6ef390573b39bb9e32bf3180c))

## [1.2.1](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.2.0...1.2.1) (2024-01-18)


### Bug Fixes

* Add task to AI gateway queue, if the image is not found ([97690ff](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/97690ff9d4cdf651bd606506d96ff283035667f1))
* added code to update the docserver with results ([02deedf](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/02deedffac8ba96b56eb33697d3e297158cbb1fa))
* changed num_face to num_faces_final for the processed faces ([70adc40](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/70adc406e26b69f7419bb0d1b850048634a276fa))
* removed blurriness from the post processing logic ([15f528b](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/15f528b6f29f5fcf690919d49daebc843ed50926))
* updated the code for similar-images-by-face ([daccdf1](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/daccdf1b6da1495d422e2925e2a442e7809a73cb))

# [1.2.0](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.1.0...1.2.0) (2024-01-12)


### Bug Fixes

* added face_id to video processing responce ([ef22afc](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/ef22afce62a674bdd3fc0e0d1091b1c8b96f44f6))
* bug fix in relationship creation ([0275415](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/0275415a6bd5808b593024895148366ff515731a))
* numpy callback error ([fb71704](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/fb71704f4df6689bd33a3007bc0e297185ca89a9))
* recorded processing times for statistics, added small model to compare the speeds. ([1d04516](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/1d0451679193706830b05cd1a1e8c78500285e45))
* small bug fix in logging ([c967b8d](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/c967b8d50b50ed3c6008a9daa3b25b25b2e437a4))
* small bug fix in relationship creation ([73be50a](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/73be50a322cfc92d05376963b45721503bef732a))
* updated the similar-images response ([59100d6](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/59100d63f24737a4ece72df9ade068c1970679c3))


### Features

* redaction condition implmented ([4e49dc9](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/4e49dc978b465822ea140379d21b77b5d097e973))

# [1.1.0](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.0.3...1.1.0) (2024-01-10)


### Bug Fixes

* added code for relationship creation ([2453fb6](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/2453fb68335e33af3f713d20d94430defd956717))
* added code to create relationships ([01f34ab](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/01f34abc44c5233f4933415705b0c31a6b2f5a2c))
* added logic to check for blurriness ([8ccc8aa](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/8ccc8aa437ef37e86ff9c8415931e0e96c8a91c6))
* added person entity and created relation between image and face ([708ee13](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/708ee13d55492f4035f4d41cedf1aa0907c819c8))
* bug fix. updated code to detect GPU ([6ae9950](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/6ae995034342450df66e55133eb4846d84853128))
* fixed the issue of bbox values going negative from detection ([b55ea21](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/b55ea2125c0f464b05aad7191af15861969f00c3))


### Features

* param to switch fr focus to redaction ([f50c263](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/f50c263da8ae538faff1841d8c38bd6c1df7ed0c))

## [1.0.3](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.0.2...1.0.3) (2024-01-05)


### Bug Fixes

* added another logic to check the GPU status ([3f99273](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/3f99273b5f949bde170734c33d7a8936c65c8e47))

## [1.0.2](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.0.1...1.0.2) (2024-01-05)


### Bug Fixes

* added allignment condition to filter out side faces ([b284a9f](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/b284a9f51a2cc4d86080ecb5e804333c7e5b2f48))
* added crawler info as optional ([d2d2e7c](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/d2d2e7c50d182e0ca5f5923b0d58f5d2fdce90dc))
* added few fields to the VDB ([4493ef3](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/4493ef3dfabf88c986c6a4f2c1d0caa8f3a071f9))
* fixed thebug in encoding ([be7f8e7](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/be7f8e77d0af1a3337d6bbb3fd2c192eebf0026f))

## [1.0.1](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/compare/1.0.0...1.0.1) (2024-01-03)


### Bug Fixes

* added endpoint to check the similarity between faces in two images ([e7fa125](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/e7fa125e133c77a50a74f978230f2e19f36cc666))

# 1.0.0 (2024-01-03)


### Bug Fixes

* added another endpoint ([dd58c78](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/dd58c784c0a64018966655967714e0ebf9c9a1d2))
* added default value to entity id as Default ([45a7c65](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/45a7c653115be763b5aa5e115a94c7d08676ad47))
* added dependencies ([107dd89](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/107dd8994801e9930e46f7264d0d654e74f6ed00))
* added face size thresholding to the image processing ([9fffeb2](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/9fffeb2ed4c3d7553231d0093bb9a6fc0ad4517c))
* added few build essentials to the docker file ([ecde18f](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/ecde18ff080bffa844488d927991bdd2c45e9235))
* added few try catch blocks ([1f57a31](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/1f57a31f2755bdb21f18ca90a09d0c8a5556a1f0))
* added get_similar imges endpoint ([edea03b](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/edea03b926b76cc27589f4d87be7e54424c481c2))
* added GPU inference to the code ([0768ce0](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/0768ce0144cf675607d2e78531d42ff72ba755ac))
* added logs ([fa71ee3](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/fa71ee34b0be6e11eea31b2f5bc554f5b6a53069))
* added non-interactive docker isntallation args ([02da98d](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/02da98d8c1f5dda8526fda901041ee04649581a7))
* added requirements packages ([51f2b01](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/51f2b019c7dcf2dbda64d19c163efde23e467c31))
* added setup tools to the dockerfile ([9c1e13c](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/9c1e13cb230b7735b53102ff438e19055143d041))
* added uninstall onnx cpu ([99f3d10](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/99f3d10c83122a6e9a272ea21c0380c118115db9))
* added video_fr ([fa85276](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/fa85276a510607aa2f38142942bc640f881f8557))
* added without tqdm endpoint ([4cf11d6](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/4cf11d6fc3b6663a490eb9820e87256ff326794c))
* bug fix in processd image creation ([8588196](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/8588196e8a32e8a9714f1222249d7f2164253dd8))
* bug fix in type conversion ([56b41d8](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/56b41d8fa4619ce44ae32afd2f9027a46c3ed3bb))
* bug fix in vector db inserting, inserting if num entites is greater than 0 ([03913c0](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/03913c047aec5b6a75992a802a2c0bd2b86fd5c7))
* bug in type conversions ([4e4e8a1](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/4e4e8a13b1a007082490ff458cc1f28134972f42))
* changed port in main.py ([e0e7dd6](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/e0e7dd65640eed98b7bcb11746379e1cfed61d29))
* changed the base iamge to just python because of no space left ([0cb8c00](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/0cb8c0030e87658d452fd9d655d3f682e96282bb))
* changed the base image ([9ba10c2](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/9ba10c2cc20de47f6443f9e4a2396071842956aa))
* changed the base image ([a97f9b6](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/a97f9b6c31d5f9ff153f533c90ba024f4a36cd35))
* changed the base image again to pytorch base ([29f5e4e](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/29f5e4edc620b9d3ecc575d4dcea6d843f97b72a))
* changed the base image and trying building again using on-premise pool ([62901a9](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/62901a9fc79ea0395fac426e64431716c0a465cd))
* changed the base image to nvidia cuda toolkit ([baa5c6a](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/baa5c6a43ffac79aad47b8dc37d40d61a0cc126c))
* Changed the base image, Add vector database integration and normalize face embeddings ([055fdc2](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/055fdc2c186ab472386d2ed39474a03e5b0f4c32))
* changed the base version ([850cf9d](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/850cf9d7420110dcae8be9af8ebd59cdb1333f8b))
* changed the cuda base image ([7269a60](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/7269a60189dc68b1ab2e3049117516fb5b48eafd))
* changed the input to body in the process-image endpoint ([d5301ec](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/d5301ecf321f1044bc98e19d7f14f3c82bf62596))
* changed the onnx version ([f2f5aa2](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/f2f5aa23d6c5256e1691b9f12ce60c419bc3897a))
* changed the onnx version ([a0b2136](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/a0b21368a229fe66a5d3ccc03d248e29e9d8f2e6))
* changed the onnx-runtime to GPU ([c8edea9](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/c8edea949976e9dafce40050a5a53d3d2fa4b039))
* changed the providers to CUDA ([c3065c2](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/c3065c2dffb8a3dc83502373f4821f73afae05db))
* changed the return statement ([0c8d658](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/0c8d65811a99e19164f93e8cbcb9ee59ae775016))
* edited the reponse, deleted message key (no need) ([5d759db](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/5d759db1935a2e3a55665008e6064cb66ff76c73))
* first commit ([09dbf11](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/09dbf114f67295e87733914ee5843c28b575bbfe))
* fix in azure-pipelines.yml ([f06813d](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/f06813db14b8d79bb8f41d3ddaa2daae29350b11))
* fixed the code to make embedding valid in reponse ([88574da](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/88574da3dc9250f86f9620b8d75b86bab6126f32))
* imported pytorch and displaying cuda availablity info ([5bbf05e](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/5bbf05e6d347a7b3667657153f83c0c67e5b3ac1))
* importing torch library and adding logs ([c55f677](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/c55f67721e8bcf5f33c7d974658e19aa61899565))
* modified image processing endpoint ([0393cd2](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/0393cd292d9979bfee4ed68d42b4f80300f6af17))
* modified requirement.txt ([26b573a](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/26b573a0a5dac7c50ee9bc74f9dce834780ba76e))
* modified video processing endpoint ([e3e451c](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/e3e451c5bc10553924f62252af6cfc24b89393ba))
* reduced the face size threshold to 30 pixels from 40 ([78d6d70](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/78d6d70b181ab4f3a7819b0692acb7c78006da99))
* removed om-premise ([17161b0](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/17161b0b6bcbcd0304fa45b17428cd79974b49b8))
* removed on premise ([96c5387](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/96c5387891e08492081ee9a22ad36c6b255c3009))
* removed on-premise ([b857837](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/b857837cd71a264f5d19fcc0289fb108d86d5e65))
* removed on-premise ([89898b4](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/89898b4ad3aa0de9717dfd0ebe6a7ef37a8dd901))
* removed on-premise because it is getting error at cleanup ([510a4cb](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/510a4cbe0a2fe85a1ea3b020d4c6cfe21645904a))
* removed the platform architecture info in the azure-pipeline ([14f7fdd](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/14f7fdda166ea5ee29099ffadd06dfcc56e4aed9))
* removed the torc import ([5765e9a](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/5765e9a38e569fda45fc9ce58b2b325a7be655b8))
* reverted back to the previous base image because there is no space left ([7150adb](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/7150adb69e88e5820c6807e9e906d6a7de9fa6e5))
* reverting back to the previous base image ([9453e02](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/9453e02cc9ebb6460cde01d36bcda22724b8ff67))
* sending error message callback in case of error in the code ([3e84521](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/3e8452198a8d3b1db1f07e9f663d3fee11f189b5))
* set on pool on-premise ([fea238c](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/fea238c8b0ec3ca9d50169e41c61106777ba2c01))
* set on premise pool ([6c36524](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/6c3652428981af0ea1a37139e9d1ca597b7a1bca))
* set on-premise ([224610f](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/224610f707dacbd6a1213c5a429219f3a824d3f4))
* set the pool to on-premise ([eb4865b](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/eb4865bb9458c126cbaf6908beeac8a730a101c7))
* tried uninstalling and installing numpy ([392be83](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/392be831785dfcb909ca565113dc780e3fe98037))
* try to optimize the docker size ([6a616df](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/6a616dfc7025277190572a6ed70703e660f92dcd))
* Update numpy installation in Dockerfile ([82c0082](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/82c00823c8153b589172102633ed5c829ab48ef2))
* updated docker-compose file ([6c4a05a](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/6c4a05aaf481334730d6e8f523edc0380d2c31cc))
* Updated docker-compose.yml ([150477d](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/150477dbcd9103042b9e2f0e12e5061e887f3fed))
* Updated Dockerfile cahnged port from 8089 to 8000 ([549c951](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/549c9513bf85d7dcdd56746135da3506c05bd361))
* updated logs ([f0aa517](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/f0aa517d8d77cdd804bccaf4dab29cb52f63ea36))
* updated the base image to latest version ([bf56daa](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/bf56daa3337142fa506ac2ac08b4ed1f78e00d54))
* updated the endpoint for processing video ([86cffa6](https://dev.azure.com/predictintel/data-ai/_git/facial-recognition-v2/commit/86cffa60ef74fd3e7a23d4a8e0478f82e00e5605))
